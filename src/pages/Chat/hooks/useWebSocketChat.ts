/**
 * 🔥 Chainlit WebSocket聊天Hook
 * 升级SSE到WebSocket，保持所有Chainlit架构优势，提升性能到<20ms延迟
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import type { MessageInstance } from 'antd/es/message/interface';
import { chainlitPerfMonitor } from '../utils/chainlitPerformanceTest';
import { ChainlitWebSocketManager, type WebSocketConnectionState } from '../utils/webSocketManager';
import { startEventDebugging } from '../utils/debugEvents';

export interface UseWebSocketChatOptions {
  autoConnect?: boolean;
  enablePerformanceMonitoring?: boolean;
  messageAcknowledgment?: boolean;
}

export const useWebSocketChat = (
  messageApi: MessageInstance,
  options: UseWebSocketChatOptions = {}
) => {
  const {
    autoConnect = true,
    enablePerformanceMonitoring = true,
    messageAcknowledgment = true,
  } = options;

  // WebSocket管理器
  const wsManagerRef = useRef<ChainlitWebSocketManager | null>(null);

  // 连接状态
  const [connectionState, setConnectionState] = useState<WebSocketConnectionState>('disconnected');
  const [connectionLatency, setConnectionLatency] = useState<number | undefined>();
  const [reconnectCount, setReconnectCount] = useState(0);

  // 聊天状态
  const [streaming, setStreaming] = useState(false);
  const [currentMessageId, setCurrentMessageId] = useState<string | null>(null);

  // 初始化WebSocket管理器
  useEffect(() => {
    if (!wsManagerRef.current) {
      wsManagerRef.current = new ChainlitWebSocketManager();

      // 🔥 Chainlit核心：设置事件监听器
      wsManagerRef.current.onConnectionStateChange((state) => {
        console.log(`[CHAINLIT-WS-HOOK] Connection state: ${state}`);
        setConnectionState(state);

        if (state === 'connected') {
          const metrics = chainlitPerfMonitor.getMetrics();
          setConnectionLatency(metrics.connectionLatency);
          setReconnectCount(0);
        } else if (state === 'reconnecting') {
          setReconnectCount(prev => prev + 1);
        }
      });

      wsManagerRef.current.onMessageReceived((data) => {
        handleWebSocketMessage(data);
      });

      wsManagerRef.current.onErrorOccurred((error) => {
        console.error('[CHAINLIT-WS-HOOK] WebSocket error:', error);
        messageApi.error('WebSocket连接错误');
      });
    }

    // 自动连接
    if (autoConnect && wsManagerRef.current.getConnectionState() === 'disconnected') {
      connectWebSocket();
    }

    // 🔥 开发模式：启动事件调试
    if (process.env.NODE_ENV === 'development') {
      startEventDebugging();
    }

    return () => {
      // 清理资源
      if (wsManagerRef.current) {
        wsManagerRef.current.disconnect();
      }
    };
  }, [autoConnect, messageApi]);

  // 🔥 Chainlit核心：连接WebSocket
  const connectWebSocket = useCallback(async () => {
    if (!wsManagerRef.current) return;

    try {
      await wsManagerRef.current.connect();
    } catch (error) {
      console.error('[CHAINLIT-WS-HOOK] Failed to connect:', error);
      messageApi.error('WebSocket连接失败');
    }
  }, [messageApi]);

  // 🔥 Chainlit核心：断开WebSocket
  const disconnectWebSocket = useCallback(() => {
    if (wsManagerRef.current) {
      wsManagerRef.current.disconnect();
      messageApi.info('WebSocket已断开');
    }
  }, [messageApi]);

  // 🔥 Chainlit核心：处理WebSocket消息
  const handleWebSocketMessage = useCallback((data: any) => {
    const receiveTime = Date.now();

    try {
      console.log('[CHAINLIT-WS-HOOK] ⚡ Processing WebSocket message:', data);

      // 🔥 BaseResult格式验证：检查响应格式（保持后端兼容性）
      if (data.code !== 200) {
        console.error('[CHAINLIT-WS-HOOK] ❌ Error response:', data);
        if (data.message) {
          messageApi.error(data.message);
        }
        return;
      }

      // 🔥 BaseResult格式：从data字段中提取实际数据
      const responseData = data.data;
      if (!responseData) {
        console.warn('[CHAINLIT-WS-HOOK] ⚠️ No data in BaseResult response');
        return;
      }

      // 🔥 Chainlit核心：立即处理数据，零延迟
      processDataChainlitStyle(responseData, receiveTime);

    } catch (error) {
      console.error('[CHAINLIT-WS-HOOK] ❌ Message processing error:', error);
    }
  }, [messageApi, processDataChainlitStyle]);

  // 🔥 Chainlit核心：同步数据处理器（消除所有延迟）
  const processDataChainlitStyle = useCallback((data: any, receiveTime: number) => {
    const chunkType = data.chunk_type || 'unknown';
    const chunkSubType = data.chunk_sub_type || 'default';
    const processStartTime = Date.now();
    const processDelay = processStartTime - receiveTime;

    // 🔥 关键修复：从后端响应中提取message_id
    const backendMessageId = data.message_id;
    const effectiveMessageId = backendMessageId || currentMessageId;

    // 🔥 Chainlit性能分析：实时性能指标
    console.log(`[CHAINLIT-WS-HOOK] 📊 Event: ${chunkType}/${chunkSubType}, backendMessageId: ${backendMessageId}, currentMessageId: ${currentMessageId}, effectiveMessageId: ${effectiveMessageId}`);
    console.log(`[CHAINLIT-WS-HOOK] ⚡ Process delay: ${processDelay}ms`);
    console.log(`[CHAINLIT-WS-HOOK] 📋 Data:`, data);

    // 🔥 分析delta内容以便调试
    if (data.delta?.length > 0) {
      const deltaItem = data.delta[0];
      console.log(`[CHAINLIT-WS-HOOK] 📝 Delta analysis: role=${deltaItem.role}, contentLength=${deltaItem.content?.length || 0}, messageType=${deltaItem.message_type}`);
    }

    // 记录性能指标
    if (enablePerformanceMonitoring && data.delta?.length > 0) {
      const contentLength = data.delta[0].content?.length || 0;
      chainlitPerfMonitor.recordChunk(receiveTime, processStartTime, contentLength);
    }

    // 🔥 Chainlit模式：处理不同类型的消息块
    if (chunkType === 'Event' && data.delta?.length > 0) {
      // 处理流式更新事件 - 增量内容追加
      const deltaItem = data.delta[0];
      console.log(`[CHAINLIT-WS-HOOK] 📝 Processing streaming Event delta:`, deltaItem);

      if (deltaItem.role === 'assistant' && deltaItem.content && effectiveMessageId) {
        console.log(`[CHAINLIT-WS-HOOK] 🚀 Dispatching streaming update - messageId: ${effectiveMessageId}, content: "${deltaItem.content}" (${deltaItem.content.length} chars)`);

        // 🔥 流式更新：追加内容
        const updateEvent = new CustomEvent('chainlit-message-update', {
          detail: {
            messageId: effectiveMessageId,
            content: deltaItem.content,
            chunkSubType,
            isStreaming: true,
            updateType: 'append', // 🔥 明确标记为追加操作
          }
        });
        window.dispatchEvent(updateEvent);
      } else {
        console.log(`[CHAINLIT-WS-HOOK] ⚠️ Skipping Event delta - role: ${deltaItem.role}, content: ${!!deltaItem.content}, effectiveMessageId: ${effectiveMessageId}`);
      }

      //
      // chunkType === 'Event'

      // chunkSubType === 'ModelClientStreamingChunkEvent' 流式
      // chunkSubType === 'OssUrlMessage' 图片url
      // chunkSubType === 'ToolCallRequestEvent' 工具调用请求
      // chunkSubType === 'ToolResultMessage' 工具调用结果
      //
      //   'ThoughtEvent' //TODO

    } else if (chunkType === 'Message') {
            // chunkType === 'Message'
            //
            // chunkSubType === 'TextMessage'
            // chunkSubType === 'OssUrlMessage'

      // 处理完成事件 - 完整内容替换
      console.log(`[CHAINLIT-WS-HOOK] ✅ Processing completion Message event`);

      if (effectiveMessageId) {
        // 🔥 检查是否有完整内容需要替换流式内容
        let completeContent = null;
        if (data.delta?.length > 0) {
          const deltaItem = data.delta[0];
          if (deltaItem.role === 'assistant' && deltaItem.content) {
            completeContent = deltaItem.content;
            console.log(`[CHAINLIT-WS-HOOK] 🔄 Complete content received for replacement - messageId: ${effectiveMessageId}, length: ${completeContent.length} chars`);
            console.log(`[CHAINLIT-WS-HOOK] 📋 Complete content preview: "${completeContent.substring(0, 100)}${completeContent.length > 100 ? '...' : ''}"`);
          } else {
            console.log(`[CHAINLIT-WS-HOOK] ⚠️ Message completion without content - role: ${deltaItem?.role}, hasContent: ${!!deltaItem?.content}`);
          }
        } else {
          console.log(`[CHAINLIT-WS-HOOK] ⚠️ Message completion with empty delta array`);
        }

        // 🔥 发送完成事件
        const completeEvent = new CustomEvent('chainlit-message-complete', {
          detail: {
            messageId: effectiveMessageId,
            chunkSubType,
            completeContent, // 🔥 传递完整内容用于替换
            updateType: 'replace', // 🔥 明确标记为替换操作
          }
        });
        window.dispatchEvent(completeEvent);
      }
    } else if (data.delta?.length > 0) {
      // 🔥 处理未知chunk类型但有delta内容的情况
      const deltaItem = data.delta[0];
      console.log(`[CHAINLIT-WS-HOOK] ⚠️ Unknown chunk type "${chunkType}" with delta content:`, deltaItem);

      if (deltaItem.role === 'assistant' && deltaItem.content && effectiveMessageId) {
        // 🔥 对于未知类型，根据内容长度判断是增量还是完整内容
        const isLikelyCompleteContent = deltaItem.content.length > 100; // 启发式判断
        const updateType = isLikelyCompleteContent ? 'replace' : 'append';

        console.log(`[CHAINLIT-WS-HOOK] 🤔 Treating unknown chunk as ${updateType} based on content length (${deltaItem.content.length} chars)`);

        const updateEvent = new CustomEvent('chainlit-message-update', {
          detail: {
            messageId: effectiveMessageId,
            content: deltaItem.content,
            chunkSubType,
            isStreaming: !isLikelyCompleteContent,
            updateType,
          }
        });
        window.dispatchEvent(updateEvent);
      }
    }

    // 🔥 Chainlit：处理artifacts
    if (data.artifacts?.length > 0 && effectiveMessageId) {
      const artifactsEvent = new CustomEvent('chainlit-artifacts-update', {
        detail: {
          messageId: effectiveMessageId,
          artifacts: data.artifacts,
        }
      });
      window.dispatchEvent(artifactsEvent);
    }

    // 🔥 Chainlit：处理流完成
    if (data.finish_reason === 'stop') {
      console.log(`[CHAINLIT-WS-HOOK] 🏁 Stream finished: ${data.finish_reason}`);
      setStreaming(false);
      setCurrentMessageId(null);
      console.log(`[CHAINLIT-WS-HOOK] 🏁 WebSocket streaming state set to false`);

      if (effectiveMessageId) {
        const finishEvent = new CustomEvent('chainlit-stream-finish', {
          detail: {
            messageId: effectiveMessageId,
            finishReason: data.finish_reason,
            usageInfo: data.usage_info,
          }
        });
        window.dispatchEvent(finishEvent);
      }
    }

    // 🔥 Chainlit优化：性能监控
    const endTime = Date.now();
    const totalDelay = endTime - receiveTime;
    console.log(`[CHAINLIT-WS-HOOK] ⚡ Total: ${totalDelay}ms (${totalDelay < 20 ? '✅ FAST' : '❌ SLOW'})`);
  }, [currentMessageId, enablePerformanceMonitoring, setStreaming, setCurrentMessageId]);

  // 🔥 Chainlit核心：发送WebSocket消息
  const sendWebSocketMessage = useCallback(async (
    sessionId: string,
    content: string,
    cid: string,
    userId: string,
    user_name: string,
    messageId: string
  ) => {
    if (!wsManagerRef.current || !wsManagerRef.current.isConnected()) {
      throw new Error('WebSocket not connected');
    }

    setStreaming(true);
    setCurrentMessageId(messageId);
    console.log(`[CHAINLIT-WS-HOOK] 🚀 WebSocket streaming state set to true, messageId: ${messageId}`);
    console.log(`[CHAINLIT-WS-HOOK] 🔍 Current message ID set to: ${messageId}`);

    // 🔥 启动Chainlit性能监控
    if (enablePerformanceMonitoring) {
      chainlitPerfMonitor.start();
    }

    const requestPayload = {
      session_id: sessionId,
      user_id: userId,
      cid: cid,
      uid: userId,
      user_name: user_name,
      ai_message_id: messageId,  // 🔥 添加前端生成的AI消息ID
      contents: [
        {
          role: 'user',
          content: content.trim(),
          message_type: 'text',
          cid: cid,
          uid: userId,
          user_name: user_name
        }
      ],
      metadata: {},
      stream: true
    };

    try {
      console.log('[CHAINLIT-WS-HOOK] 📤 Sending WebSocket message...');

      await wsManagerRef.current.sendMessage(
        requestPayload,
        messageAcknowledgment
      );

      console.log('[CHAINLIT-WS-HOOK] ✅ Message sent successfully');

    } catch (error) {
      console.error('[CHAINLIT-WS-HOOK] ❌ Failed to send message:', error);
      setStreaming(false);
      setCurrentMessageId(null);
      throw error;
    }
  }, [enablePerformanceMonitoring, messageAcknowledgment]);

  // 获取性能指标
  const getPerformanceMetrics = useCallback(() => {
    return chainlitPerfMonitor.getMetrics();
  }, []);

  // 检查是否符合Chainlit标准
  const isChainlitCompliant = useCallback(() => {
    return chainlitPerfMonitor.isChainlitCompliant();
  }, []);

  return {
    // WebSocket状态
    connectionState,
    connectionLatency,
    reconnectCount,
    isConnected: connectionState === 'connected',

    // 聊天状态
    streaming,

    // 方法
    connectWebSocket,
    disconnectWebSocket,
    sendWebSocketMessage,

    // 性能监控
    getPerformanceMetrics,
    isChainlitCompliant,
  };
};
